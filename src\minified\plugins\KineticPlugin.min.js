/*!
 * VERSION: 0.5.2
 * DATE: 2014-07-17
 * UPDATES AND DOCS AT: http://www.greensock.com
 *
 * @license Copyright (c) 2008-2015, GreenSock. All rights reserved.
 * This work is subject to the terms at http://greensock.com/standard-license or for
 * Club GreenSock members, the software agreement that was issued with your membership.
 * 
 * @author: <PERSON>, <EMAIL>
 */
var _gsScope="undefined"!=typeof module&&module.exports&&"undefined"!=typeof global?global:this||window;(_gsScope._gsQueue||(_gsScope._gsQueue=[])).push(function(){"use strict";var t,e,i,r,s={scale:1,shadowOffset:1,fillPatternOffset:1,offset:1,fill:2,stroke:2,shadowColor:2},n={},a={},o=/(\d|\.)+/g,l=/(?:_cw|_ccw|_short)/,h=_gsScope._gsDefine.globals.com.greensock.plugins,u={aqua:[0,255,255],lime:[0,255,0],silver:[192,192,192],black:[0,0,0],maroon:[128,0,0],teal:[0,128,128],blue:[0,0,255],navy:[0,0,128],white:[255,255,255],fuchsia:[255,0,255],olive:[128,128,0],yellow:[255,255,0],orange:[255,165,0],gray:[128,128,128],purple:[128,0,128],green:[0,128,0],red:[255,0,0],pink:[255,192,203],cyan:[0,255,255],transparent:[255,255,255,0]},f=function(t,e,i){return t=0>t?t+1:t>1?t-1:t,0|255*(1>6*t?e+6*(i-e)*t:.5>t?i:2>3*t?e+6*(i-e)*(2/3-t):e)+.5},c=function(t){if(""===t||null==t||"none"===t)return u.transparent;if(u[t])return u[t];if("number"==typeof t)return[t>>16,255&t>>8,255&t];if("#"===t.charAt(0))return 4===t.length&&(t="#"+t.charAt(1)+t.charAt(1)+t.charAt(2)+t.charAt(2)+t.charAt(3)+t.charAt(3)),t=parseInt(t.substr(1),16),[t>>16,255&t>>8,255&t];if("hsl"===t.substr(0,3)){t=t.match(o);var e=Number(t[0])%360/360,i=Number(t[1])/100,r=Number(t[2])/100,s=.5>=r?r*(i+1):r+i-r*i,n=2*r-s;return t.length>3&&(t[3]=Number(t[3])),t[0]=f(e+1/3,n,s),t[1]=f(e,n,s),t[2]=f(e-1/3,n,s),t}for(var a=t.match(o)||u.transparent,l=a.length;--l>-1;)a[l]=Number(a[l]);return a},p=function(t,e,i,r){this.getter=e,this.setter=i;var s=c(t[e]());this.proxy={r:s[0],g:s[1],b:s[2],a:s.length>3?s[3]:1},r&&(this._next=r,r._prev=this)},_=[],d=function(){var i=_.length;if(0!==i){for(;--i>-1;)_[i].draw(),_[i]._gsDraw=!1;_.length=0}else t.removeEventListener("tick",d),e=!1},g=function(t,e){var i="x"===e?"y":"x",r="_gs_"+t;n[t]=function(){return this["get"+t]()[e]},a[t]=function(s){var n=this["get"+t](),a=this[r];return a||(a=this[r]={}),a[e]=s,a[i]=n[i],this[t](a),this}},m=function(t,e){return function(i){return arguments.length?e(i):t()}},v=function(t,e){var i,r,o,l,h=[];for(i in e)if(r=e[i],"bezier"!==i&&"autoDraw"!==i&&"set"!==i.substr(0,3)&&void 0===t[i]&&(h.push(i),delete e[i],i="set"+i.charAt(0).toUpperCase()+i.substr(1),e[i]=r),s[i]){if(1===s[i])return e[i+"X"]=e[i+"Y"]=e[i],delete e[i],v(t,e);!t[i]&&a[i]&&(l=t.prototype||t,l[i]=m(n[i],a[i]))}else if("bezier"===i)for(r=r instanceof Array?r:r.values||[],o=r.length;--o>-1;)0===o?h=h.concat(v(t,r[o])):v(t,r[o]);return h},y=function(t){var e,i={};for(e in t)i[e]=t[e];return i};for(r in s)1===s[r]&&(g(r,"x"),g(r,"y"));var x=_gsScope._gsDefine.plugin({propName:"kinetic",API:2,version:"0.5.2",init:function(e,r,n){var a,o,u,f,_,d;if(!i&&(i=5>parseInt(Kinetic.version.split(".")[0],10)))throw"The GSAP KineticPlugin that's loaded requires KineticJS version 5.0.0 or later. For earlier versions, use KineticPlugin from GSAP 1.11.3 or earlier.";this._overwriteProps=v(e,r),this._target=e,this._layer=r.autoDraw!==!1?e.getLayer():null,!t&&this._layer&&(t=n.constructor.ticker);for(a in r){if(o=r[a],2===s[a])f=this._firstSP=new p(e,a,a,this._firstSP),o=c(o),f.proxy.r!==o[0]&&this._addTween(f.proxy,"r",f.proxy.r,o[0],a),f.proxy.g!==o[1]&&this._addTween(f.proxy,"g",f.proxy.g,o[1],a),f.proxy.b!==o[2]&&this._addTween(f.proxy,"b",f.proxy.b,o[2],a),(o.length>3||1!==f.proxy.a)&&f.proxy.a!==o[3]&&this._addTween(f.proxy,"a",f.proxy.a,o.length>3?o[3]:1,a);else if("bezier"===a){if(_=h.BezierPlugin,!_)throw"BezierPlugin not loaded";_=this._bezier=new _,"object"==typeof o&&o.autoRotate===!0&&(o.autoRotate=["x","y","rotation",0,!1]),_._onInitTween(e,o,n),this._overwriteProps=this._overwriteProps.concat(_._overwriteProps),this._addTween(_,"setRatio",0,1,a)}else if("rotation"!==a&&"rotationDeg"!==a||"string"!=typeof o||!l.test(o))o instanceof Array?this._initArrayTween(e[a](),o,a):"autoDraw"!==a&&(u="get"+a.substr(3),this._addTween(e,a,("function"==typeof e[a]?e["get"!==u&&"function"==typeof e[u]?u:a]():e[a])||0,o,a));else{if(d=h.DirectionalRotationPlugin,!d)throw"DirectionalRotationPlugin not loaded";d=this._directionalRotation=new d,u={useRadians:!1},u[a]=o,d._onInitTween(e,u,n),this._addTween(d,"setRatio",0,1,a)}this._overwriteProps.push(a)}return!0},kill:function(t){return t=y(t),v(this._target,t),this._bezier&&this._bezier._kill(t),this._directionalRotation&&this._directionalRotation._kill(t),this._super._kill.call(this,t)},round:function(t,e){return t=y(t),v(this._target,t),this._bezier&&this._bezier._roundProps(t,e),this._super._roundProps.call(this,t,e)},set:function(i){this._super.setRatio.call(this,i);var r,s,n,a,o,l,h=this._firstSP,u=this._layer,f=this._arrayTweens;if(h)for(o=this._target;h;)l=h.proxy,o[h.setter]((1!==l.a?"rgba(":"rgb(")+(0|l.r)+", "+(0|l.g)+", "+(0|l.b)+(1!==l.a?", "+l.a:"")+")"),h=h._next;if(f){for(r=f.length;--r>-1;)s=f[r],a=s.s+s.c*i,s.a[s.i]=1e-6>a&&a>-1e-6?0:a;for(n in this._arrayProps)this._target[n](this._arrayProps[n])}u&&!u._gsDraw&&(_.push(u),u._gsDraw=!0,e||(t.addEventListener("tick",d),e=!0))}});r=x.prototype,r._initArrayTween=function(t,e,i){this._arrayTweens||(this._arrayTweens=[],this._arrayProps={});for(var r,s,n=t.length,a=this._arrayTweens;--n>-1;)r=t[n],s=e[n],r!==s&&a.push({a:t,i:n,s:r,c:s-r});a.length&&(this._arrayProps[i]=t)}}),_gsScope._gsDefine&&_gsScope._gsQueue.pop()();