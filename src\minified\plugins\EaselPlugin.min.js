/*!
 * VERSION: 0.1.6
 * DATE: 2014-07-17
 * UPDATES AND DOCS AT: http://www.greensock.com
 *
 * @license Copyright (c) 2008-2015, GreenSock. All rights reserved.
 * This work is subject to the terms at http://greensock.com/standard-license or for
 * Club GreenSock members, the software agreement that was issued with your membership.
 * 
 * @author: <PERSON>, <EMAIL>
 **/
var _gsScope="undefined"!=typeof module&&module.exports&&"undefined"!=typeof global?global:this||window;(_gsScope._gsQueue||(_gsScope._gsQueue=[])).push(function(){"use strict";var t,e,i=/(\d|\.)+/g,r=["redMultiplier","greenMultiplier","blueMultiplier","alphaMultiplier","redOffset","greenOffset","blueOffset","alphaOffset"],s={aqua:[0,255,255],lime:[0,255,0],silver:[192,192,192],black:[0,0,0],maroon:[128,0,0],teal:[0,128,128],blue:[0,0,255],navy:[0,0,128],white:[255,255,255],fuchsia:[255,0,255],olive:[128,128,0],yellow:[255,255,0],orange:[255,165,0],gray:[128,128,128],purple:[128,0,128],green:[0,128,0],red:[255,0,0],pink:[255,192,203],cyan:[0,255,255],transparent:[255,255,255,0]},n=function(t){return""===t||null==t||"none"===t?s.transparent:s[t]?s[t]:"number"==typeof t?[t>>16,255&t>>8,255&t]:"#"===t.charAt(0)?(4===t.length&&(t="#"+t.charAt(1)+t.charAt(1)+t.charAt(2)+t.charAt(2)+t.charAt(3)+t.charAt(3)),t=parseInt(t.substr(1),16),[t>>16,255&t>>8,255&t]):t.match(i)||s.transparent},a=function(e,i,s){if(!t&&(t=_gsScope.ColorFilter||_gsScope.createjs.ColorFilter,!t))throw"EaselPlugin error: The EaselJS ColorFilter JavaScript file wasn't loaded.";for(var a,o,l,h,u,f=e.filters||[],c=f.length;--c>-1;)if(f[c]instanceof t){o=f[c];break}if(o||(o=new t,f.push(o),e.filters=f),l=o.clone(),null!=i.tint)a=n(i.tint),h=null!=i.tintAmount?Number(i.tintAmount):1,l.redOffset=Number(a[0])*h,l.greenOffset=Number(a[1])*h,l.blueOffset=Number(a[2])*h,l.redMultiplier=l.greenMultiplier=l.blueMultiplier=1-h;else for(u in i)"exposure"!==u&&"brightness"!==u&&(l[u]=Number(i[u]));for(null!=i.exposure?(l.redOffset=l.greenOffset=l.blueOffset=255*(Number(i.exposure)-1),l.redMultiplier=l.greenMultiplier=l.blueMultiplier=1):null!=i.brightness&&(h=Number(i.brightness)-1,l.redOffset=l.greenOffset=l.blueOffset=h>0?255*h:0,l.redMultiplier=l.greenMultiplier=l.blueMultiplier=1-Math.abs(h)),c=8;--c>-1;)u=r[c],o[u]!==l[u]&&s._addTween(o,u,o[u],l[u],"easel_colorFilter");if(s._overwriteProps.push("easel_colorFilter"),!e.cacheID)throw"EaselPlugin warning: for filters to display in EaselJS, you must call the object's cache() method first. "+e},o=[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0],l=.212671,h=.71516,u=.072169,f=function(t,e){if(!(t instanceof Array&&e instanceof Array))return e;var i,r,s=[],n=0,a=0;for(i=0;4>i;i++){for(r=0;5>r;r++)a=4===r?t[n+4]:0,s[n+r]=t[n]*e[r]+t[n+1]*e[r+5]+t[n+2]*e[r+10]+t[n+3]*e[r+15]+a;n+=5}return s},c=function(t,e){if(isNaN(e))return t;var i=1-e,r=i*l,s=i*h,n=i*u;return f([r+e,s,n,0,0,r,s+e,n,0,0,r,s,n+e,0,0,0,0,0,1,0],t)},p=function(t,e,i){isNaN(i)&&(i=1);var r=n(e),s=r[0]/255,a=r[1]/255,o=r[2]/255,c=1-i;return f([c+i*s*l,i*s*h,i*s*u,0,0,i*a*l,c+i*a*h,i*a*u,0,0,i*o*l,i*o*h,c+i*o*u,0,0,0,0,0,1,0],t)},_=function(t,e){if(isNaN(e))return t;e*=Math.PI/180;var i=Math.cos(e),r=Math.sin(e);return f([l+i*(1-l)+r*-l,h+i*-h+r*-h,u+i*-u+r*(1-u),0,0,l+i*-l+.143*r,h+i*(1-h)+.14*r,u+i*-u+r*-.283,0,0,l+i*-l+r*-(1-l),h+i*-h+r*h,u+i*(1-u)+r*u,0,0,0,0,0,1,0,0,0,0,0,1],t)},d=function(t,e){return isNaN(e)?t:(e+=.01,f([e,0,0,0,128*(1-e),0,e,0,0,128*(1-e),0,0,e,0,128*(1-e),0,0,0,1,0],t))},m=function(t,i,r){if(!e&&(e=_gsScope.ColorMatrixFilter||_gsScope.createjs.ColorMatrixFilter,!e))throw"EaselPlugin error: The EaselJS ColorMatrixFilter JavaScript file wasn't loaded.";for(var s,n,a,l=t.filters||[],h=l.length;--h>-1;)if(l[h]instanceof e){a=l[h];break}for(a||(a=new e(o.slice()),l.push(a),t.filters=l),n=a.matrix,s=o.slice(),null!=i.colorize&&(s=p(s,i.colorize,Number(i.colorizeAmount))),null!=i.contrast&&(s=d(s,Number(i.contrast))),null!=i.hue&&(s=_(s,Number(i.hue))),null!=i.saturation&&(s=c(s,Number(i.saturation))),h=s.length;--h>-1;)s[h]!==n[h]&&r._addTween(n,h,n[h],s[h],"easel_colorMatrixFilter");if(r._overwriteProps.push("easel_colorMatrixFilter"),!t.cacheID)throw"EaselPlugin warning: for filters to display in EaselJS, you must call the object's cache() method first. "+t;r._matrix=n};_gsScope._gsDefine.plugin({propName:"easel",priority:-1,version:"0.1.6",API:2,init:function(t,e){this._target=t;var i,r,s,n;for(i in e)"colorFilter"===i||"tint"===i||"tintAmount"===i||"exposure"===i||"brightness"===i?s||(a(t,e.colorFilter||e,this),s=!0):"saturation"===i||"contrast"===i||"hue"===i||"colorize"===i||"colorizeAmount"===i?n||(m(t,e.colorMatrixFilter||e,this),n=!0):"frame"===i?(this._firstPT=r={_next:this._firstPT,t:t,p:"gotoAndStop",s:t.currentFrame,f:!0,n:"frame",pr:0,type:0,r:!0},r.c="number"==typeof e[i]?e[i]-r.s:"string"==typeof e[i]?parseFloat(e[i].split("=").join("")):0,r._next&&(r._next._prev=r)):null!=t[i]&&(this._firstPT=r={_next:this._firstPT,t:t,p:i,f:"function"==typeof t[i],n:i,pr:0,type:0},r.s=r.f?t[i.indexOf("set")||"function"!=typeof t["get"+i.substr(3)]?i:"get"+i.substr(3)]():parseFloat(t[i]),r.c="number"==typeof e[i]?e[i]-r.s:"string"==typeof e[i]?parseFloat(e[i].split("=").join("")):0,r._next&&(r._next._prev=r));return!0},set:function(t){for(var e,i=this._firstPT,r=1e-6;i;)e=i.c*t+i.s,i.r?e=Math.round(e):r>e&&e>-r&&(e=0),i.f?i.t[i.p](e):i.t[i.p]=e,i=i._next;this._target.cacheID&&this._target.updateCache()}})}),_gsScope._gsDefine&&_gsScope._gsQueue.pop()();