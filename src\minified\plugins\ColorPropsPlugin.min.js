/*!
 * VERSION: beta 1.3.0
 * DATE: 2015-02-06
 * UPDATES AND DOCS AT: http://greensock.com
 *
 * @license Copyright (c) 2008-2015, GreenSock. All rights reserved.
 * This work is subject to the terms at http://greensock.com/standard-license or for
 * Club GreenSock members, the software agreement that was issued with your membership.
 * 
 * @author: <PERSON>, <EMAIL>
 **/
var _gsScope="undefined"!=typeof module&&module.exports&&"undefined"!=typeof global?global:this||window;(_gsScope._gsQueue||(_gsScope._gsQueue=[])).push(function(){"use strict";var t=/(\d|\.)+/g,e={aqua:[0,255,255],lime:[0,255,0],silver:[192,192,192],black:[0,0,0],maroon:[128,0,0],teal:[0,128,128],blue:[0,0,255],navy:[0,0,128],white:[255,255,255],fuchsia:[255,0,255],olive:[128,128,0],yellow:[255,255,0],orange:[255,165,0],gray:[128,128,128],purple:[128,0,128],green:[0,128,0],red:[255,0,0],pink:[255,192,203],cyan:[0,255,255],transparent:[255,255,255,0]},i=function(t,e,i){return t=0>t?t+1:t>1?t-1:t,0|255*(1>6*t?e+6*(i-e)*t:.5>t?i:2>3*t?e+6*(i-e)*(2/3-t):e)+.5},r=function(r){if(""===r||null==r||"none"===r)return e.transparent;if(e[r])return e[r];if("number"==typeof r)return[r>>16,255&r>>8,255&r];if("#"===r.charAt(0))return 4===r.length&&(r="#"+r.charAt(1)+r.charAt(1)+r.charAt(2)+r.charAt(2)+r.charAt(3)+r.charAt(3)),r=parseInt(r.substr(1),16),[r>>16,255&r>>8,255&r];if("hsl"===r.substr(0,3)){r=r.match(t);var s=Number(r[0])%360/360,n=Number(r[1])/100,a=Number(r[2])/100,o=.5>=a?a*(n+1):a+n-a*n,l=2*a-o;return r.length>3&&(r[3]=Number(r[3])),r[0]=i(s+1/3,l,o),r[1]=i(s,l,o),r[2]=i(s-1/3,l,o),r}return r.match(t)||e.transparent};_gsScope._gsDefine.plugin({propName:"colorProps",version:"1.3.0",priority:-1,API:2,init:function(t,e){this._target=t;var i,s,n,a;this.numFormat="number"===e.format;for(i in e)"format"!==i&&(n=r(e[i]),this._firstPT=a={_next:this._firstPT,p:i,f:"function"==typeof t[i],n:i,r:!1},s=r(a.f?t[i.indexOf("set")||"function"!=typeof t["get"+i.substr(3)]?i:"get"+i.substr(3)]():t[i]),a.s=Number(s[0]),a.c=Number(n[0])-a.s,a.gs=Number(s[1]),a.gc=Number(n[1])-a.gs,a.bs=Number(s[2]),a.bc=Number(n[2])-a.bs,(a.rgba=s.length>3||n.length>3)&&(a.as=4>s.length?1:Number(s[3]),a.ac=(4>n.length?1:Number(n[3]))-a.as),a._next&&(a._next._prev=a));return!0},set:function(t){for(var e,i=this._firstPT;i;)e=this.numFormat?i.s+t*i.c<<16|i.gs+t*i.gc<<8|i.bs+t*i.bc:(i.rgba?"rgba(":"rgb(")+(i.s+t*i.c>>0)+", "+(i.gs+t*i.gc>>0)+", "+(i.bs+t*i.bc>>0)+(i.rgba?", "+(i.as+t*i.ac):"")+")",i.f?this._target[i.p](e):this._target[i.p]=e,i=i._next}})}),_gsScope._gsDefine&&_gsScope._gsQueue.pop()();