/*!
 * VERSION: 0.2.2
 * DATE: 2014-07-17
 * UPDATES AND DOCS AT: http://www.greensock.com
 *
 * @license Copyright (c) 2008-2015, GreenSock. All rights reserved.
 * This work is subject to the terms at http://greensock.com/standard-license or for
 * Club GreenSock members, the software agreement that was issued with your membership.
 * 
 * @author: <PERSON>, <EMAIL>
 */
var _gsScope="undefined"!=typeof module&&module.exports&&"undefined"!=typeof global?global:this||window;(_gsScope._gsQueue||(_gsScope._gsQueue=[])).push(function(){"use strict";var t=/[^\d\-\.]/g,e=Math.PI/180,i=/(\d|\.)+/g,r={aqua:[0,255,255],lime:[0,255,0],silver:[192,192,192],black:[0,0,0],maroon:[128,0,0],teal:[0,128,128],blue:[0,0,255],navy:[0,0,128],white:[255,255,255],fuchsia:[255,0,255],olive:[128,128,0],yellow:[255,255,0],orange:[255,165,0],gray:[128,128,128],purple:[128,0,128],green:[0,128,0],red:[255,0,0],pink:[255,192,203],cyan:[0,255,255],transparent:[255,255,255,0]},s=function(t){return"number"==typeof t?[t>>16,255&t>>8,255&t]:""===t||null==t||"none"===t||"string"!=typeof t?r.transparent:r[t]?r[t]:"#"===t.charAt(0)?(4===t.length&&(t="#"+t.charAt(1)+t.charAt(1)+t.charAt(2)+t.charAt(2)+t.charAt(3)+t.charAt(3)),t=parseInt(t.substr(1),16),[t>>16,255&t>>8,255&t]):t.match(i)||r.transparent},n={scaleX:1,scaleY:1,tx:1,ty:1,rotation:1,shortRotation:1,skewX:1,skewY:1,scale:1},a=function(t,e){var i=t.matrix,r=1e-6,s=i.a,n=i.b,a=i.c,o=i.d,l=e?t._gsTransform||{skewY:0}:{skewY:0},h=0>l.scaleX;return l.tx=i.e-(l.ox||0),l.ty=i.f-(l.oy||0),l.scaleX=Math.sqrt(s*s+n*n),l.scaleY=Math.sqrt(o*o+a*a),l.rotation=s||n?Math.atan2(n,s):l.rotation||0,l.skewX=a||o?Math.atan2(a,o)+l.rotation:l.skewX||0,Math.abs(l.skewX)>Math.PI/2&&(h?(l.scaleX*=-1,l.skewX+=0>=l.rotation?Math.PI:-Math.PI,l.rotation+=0>=l.rotation?Math.PI:-Math.PI):(l.scaleY*=-1,l.skewX+=0>=l.skewX?Math.PI:-Math.PI)),r>l.rotation&&l.rotation>-r&&(s||n)&&(l.rotation=0),r>l.skewX&&l.skewX>-r&&(n||a)&&(l.skewX=0),e&&(t._gsTransform=l),l},o=function(t,e){return null==t?e:"string"==typeof t&&1===t.indexOf("=")?parseInt(t.charAt(0)+"1",10)*Number(t.substr(2))+e:Number(t)},l=function(i,r){var s=-1===i.indexOf("rad")?e:1,n=1===i.indexOf("=");return i=Number(i.replace(t,""))*s,n?i+r:i},h=_gsScope._gsDefine.plugin({propName:"raphael",version:"0.2.2",API:2,init:function(e,i,r){if(!e.attr)return!1;this._target=e,this._tween=r,this._props=e._gsProps=e._gsProps||{};var a,o,l,h,u,f,c;for(a in i)l=i[a],"transform"!==a?n[a]||"pivot"===a?this._parseTransform(e,i):(o=e.attr(a),this._firstPT=h={_next:this._firstPT,t:this._props,p:a,b:o,f:!1,n:"raphael_"+a,r:!1,type:0},"fill"===a||"stroke"===a?(u=s(o),f=s(l),h.e=l,h.s=Number(u[0]),h.c=Number(f[0])-h.s,h.gs=Number(u[1]),h.gc=Number(f[1])-h.gs,h.bs=Number(u[2]),h.bc=Number(f[2])-h.bs,u.length>3||f.length>3?(h.as=4>u.length?1:Number(u[3]),h.ac=(4>f.length?1:Number(f[3]))-h.as,h.type=2):h.type=1):(o="string"==typeof o?parseFloat(o.replace(t,"")):Number(o),"string"==typeof l?(c="="===l.charAt(1),l=parseFloat(l.replace(t,""))):c=!1,h.e=l||0===l?c?l+o:l:i[a],!o&&0!==o||!l&&0!==l||!(h.c=c?l:l-o)?(h.type=-1,h.i=i[a],h.s=h.c=0):h.s=o),this._overwriteProps.push("raphael_"+a),h._next&&(h._next._prev=h)):this._parseTransform(e,l);return!0},set:function(t){for(var e,i=this._firstPT;i;)e=i.c*t+i.s,i.r&&(e=Math.round(e)),i.type?1===i.type?i.t[i.p]="rgb("+(e>>0)+", "+(i.gs+t*i.gc>>0)+", "+(i.bs+t*i.bc>>0)+")":2===i.type?i.t[i.p]="rgba("+(e>>0)+", "+(i.gs+t*i.gc>>0)+", "+(i.bs+t*i.bc>>0)+", "+(i.as+t*i.ac)+")":-1===i.type&&(i.t[i.p]=i.i):i.t[i.p]=e,i=i._next;if(this._target.attr(this._props),this._transform){i=this._transform;var r=i.rotation,s=r-i.skewX,n=Math.cos(r)*i.scaleX,a=Math.sin(r)*i.scaleX,o=Math.sin(s)*-i.scaleY,l=Math.cos(s)*i.scaleY,h=1e-6,u=this._pxl,f=this._pyl;h>a&&a>-h&&(a=0),h>o&&o>-h&&(o=0),i.ox=this._pxg-(u*n+f*o),i.oy=this._pyg-(u*a+f*l),this._target.transform("m"+n+","+a+","+o+","+l+","+(i.tx+i.ox)+","+(i.ty+i.oy))}}}),u=h.prototype;u._parseTransform=function(t,i){if(!this._transform){var r,s,h,u,f,c,p,_,d,g=this._transform=a(t,!0),m=1e-6;if("object"==typeof i){if(r={scaleX:o(null!=i.scaleX?i.scaleX:i.scale,g.scaleX),scaleY:o(null!=i.scaleY?i.scaleY:i.scale,g.scaleY),tx:o(i.tx,g.tx),ty:o(i.ty,g.ty)},null!=i.shortRotation){r.rotation="number"==typeof i.shortRotation?i.shortRotation*e:l(i.shortRotation,g.rotation);var v=(r.rotation-g.rotation)%(2*Math.PI);v!==v%Math.PI&&(v+=Math.PI*(0>v?2:-2)),r.rotation=g.rotation+v}else r.rotation=null==i.rotation?g.rotation:"number"==typeof i.rotation?i.rotation*e:l(i.rotation,g.rotation);r.skewX=null==i.skewX?g.skewX:"number"==typeof i.skewX?i.skewX*e:l(i.skewX,g.skewX),r.skewY=null==i.skewY?g.skewY:"number"==typeof i.skewY?i.skewY*e:l(i.skewY,g.skewY),(s=r.skewY-g.skewY)&&(r.skewX+=s,r.rotation+=s),m>r.skewY&&r.skewY>-m&&(r.skewY=0),m>r.skewX&&r.skewX>-m&&(r.skewX=0),m>r.rotation&&r.rotation>-m&&(r.rotation=0),d=i.localPivot||i.globalPivot,"string"==typeof d?(f=d.split(","),c=Number(f[0]),p=Number(f[1])):"object"==typeof d?(c=Number(d.x),p=Number(d.y)):i.localPivot?(f=t.getBBox(!0),c=f.width/2,p=f.height/2):(f=t.getBBox(),c=f.x+f.width/2,p=f.y+f.height/2),i.localPivot?(_=t.matrix,c+=t.attr("x"),p+=t.attr("y"),this._pxl=c,this._pyl=p,this._pxg=c*_.a+p*_.c+_.e-g.tx,this._pyg=c*_.b+p*_.d+_.f-g.ty):(_=t.matrix.invert(),this._pxl=c*_.a+p*_.c+_.e,this._pyl=c*_.b+p*_.d+_.f,this._pxg=c-g.tx,this._pyg=p-g.ty)}else{if("string"!=typeof i)return;f=this._target.transform(),t.transform(i),r=a(t,!1),t.transform(f)}for(h in n)g[h]!==r[h]&&"shortRotation"!==h&&"scale"!==h&&(this._firstPT=u={_next:this._firstPT,t:g,p:h,s:g[h],c:r[h]-g[h],n:h,f:!1,r:!1,b:g[h],e:r[h],type:0},u._next&&(u._next._prev=u),this._overwriteProps.push("raphael_"+h))}}}),_gsScope._gsDefine&&_gsScope._gsQueue.pop()();