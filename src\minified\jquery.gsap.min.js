/*!
 * VERSION: 0.1.11
 * DATE: 2015-03-13
 * UPDATES AND DOCS AT: http://greensock.com/jquery-gsap-plugin/
 *
 * Requires TweenLite version 1.8.0 or higher and CSSPlugin.
 *
 * @license Copyright (c) 2013-2015, GreenSock. All rights reserved.
 * This work is subject to the terms at http://greensock.com/standard-license or for
 * Club GreenSock members, the software agreement that was issued with your membership.
 *
 * @author: <PERSON>, <EMAIL>
 */
(function(t){"use strict";var e,i,s,r=t.fn.animate,n=t.fn.stop,a=!0,o=function(t){var e,i={};for(e in t)i[e]=t[e];return i},h={overwrite:1,delay:1,useFrames:1,runBackwards:1,easeParams:1,yoyo:1,immediateRender:1,repeat:1,repeatDelay:1,autoCSS:1},l=",scrollTop,scrollLeft,show,hide,toggle,",_=l,u=function(t,e){for(var i in h)h[i]&&void 0!==t[i]&&(e[i]=t[i])},c=function(t){return function(e){return t.getRatio(e)}},f={},m=function(){var r,n,a,o=window.GreenSockGlobals||window;if(e=o.TweenMax||o.TweenLite,e&&(r=(e.version+".0.0").split("."),n=!(Number(r[0])>0&&Number(r[1])>7),o=o.com.greensock,i=o.plugins.CSSPlugin,f=o.easing.Ease.map||{}),!e||!i||n)return e=null,!s&&window.console&&(window.console.log("The jquery.gsap.js plugin requires the TweenMax (or at least TweenLite and CSSPlugin) JavaScript file(s)."+(n?" Version "+r.join(".")+" is too old.":"")),s=!0),void 0;if(t.easing){for(a in f)t.easing[a]=c(f[a]);m=!1}};t.fn.animate=function(s,n,h,l){if(s=s||{},m&&(m(),!e||!i))return r.call(this,s,n,h,l);if(!a||s.skipGSAP===!0||"object"==typeof n&&"function"==typeof n.step)return r.call(this,s,n,h,l);var c,p,d,g,v=t.speed(n,h,l),y={ease:f[v.easing]||(v.easing===!1?f.linear:f.swing)},T=this,w="object"==typeof n?n.specialEasing:null;for(p in s){if(c=s[p],c instanceof Array&&f[c[1]]&&(w=w||{},w[p]=c[1],c=c[0]),"show"===c||"hide"===c||"toggle"===c||-1!==_.indexOf(p)&&-1!==_.indexOf(","+p+","))return r.call(this,s,n,h,l);y[-1===p.indexOf("-")?p:t.camelCase(p)]=c}if(w){y=o(y),g=[];for(p in w)c=g[g.length]={},u(y,c),c.ease=f[w[p]]||y.ease,-1!==p.indexOf("-")&&(p=t.camelCase(p)),c[p]=y[p],delete y[p];0===g.length&&(g=null)}return d=function(i){var s,r=o(y);if(g)for(s=g.length;--s>-1;)e.to(this,t.fx.off?0:v.duration/1e3,g[s]);r.onComplete=function(){i?i():v.old&&t(this).each(v.old)},e.to(this,t.fx.off?0:v.duration/1e3,r)},v.queue!==!1?(T.queue(v.queue,d),"function"==typeof v.old&&T.queue(v.queue,function(t){v.old.call(this),t()})):d.call(T),T},t.fn.stop=function(t,i){if(n.call(this,t,i),e){if(i)for(var s,r=e.getTweensOf(this),a=r.length;--a>-1;)s=r[a].totalTime()/r[a].totalDuration(),s>0&&1>s&&r[a].seek(r[a].totalDuration());e.killTweensOf(this)}return this},t.gsap={enabled:function(t){a=t},version:"0.1.11",legacyProps:function(t){_=l+t+","}}})(jQuery);