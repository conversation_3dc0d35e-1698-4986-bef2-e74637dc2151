# GSAP (GreenSock Animation Platform)

#### Ultra high-performance, professional-grade animation for the modern web

GSAP is a suite of tools for scripted, high-performance HTML5 animations that work in all major browsers. No other library delivers such advanced sequencing, API efficiency, and tight control while solving real-world problems that animators face. Stop wrestling with cumbersome CSS animations, stuttery jQuery.animate() calls, or a system that limits your creativity. GSAP can animate any numeric property of any JS object, not just CSS properties.

### Getting started video

[![Getting started video](http://img.youtube.com/vi/tMP1PCErrmE/0.jpg)](http://www.youtube.com/watch?v=tMP1PCErrmE)

Think of GSAP as the Swiss Army Knife of animation...but better. It animates anything JavaScript can touch (CSS properties, canvas library objects, SVG, generic objects, whatever) and it solves countless browser inconsistencies, all with blazing speed (up to 20x faster than jQuery), including automatic GPU-acceleration of transforms. See the <a href="http://greensock.com/why-gsap/">"Why GSAP?"</a> article for details. Most other libraries only animate CSS properties. Plus, their sequencing abilities and runtime controls pale by comparison. Simply put, GSAP is the most flexible high-performance animation library on the planet, which is probably why <a href="https://developers.google.com/web/fundamentals/look-and-feel/animations/css-vs-javascript">Google recommends it</a> for JS-based animations. And unlike monolithic frameworks that dictate how you structure your apps, GSAP simply affects the animation layer; sprinkle it wherever you want.

This is the public repository for GreenSock's JavaScript tools like <a href="http://greensock.com/gsap-js/" target="_blank">GSAP</a> and <a href="http://greensock.com/draggable/" target="_blank">Draggable</a>. "GSAP" describes all of the animation-related tools which include TweenLite, TweenMax, TimelineLite, TimelineMax, various plugins (like CSSPlugin for animating CSS properties of DOM elements), extra easing functions, etc. 

### Resources

* <a href="http://www.greensock.com/gsap-js/">GSAP home page</a>
* <a href="http://www.greensock.com/get-started-js/">Getting started guide</a>
* <a href="http://greensock.com/examples-showcases">Examples &amp; showcases</a>
* <a href="http://www.greensock.com/why-gsap/">Why GSAP?</a> (a practical guide for developers)
* <a href="http://greensock.com/docs/#/HTML5/GSAP/">Full documentation</a>
* <a href="http://www.greensock.com/draggable/">Draggable demo</a>
* <a href="http://www.greensock.com/svg-tips/">Animating SVG with GSAP</a>
* <a href="http://www.greensock.com/jquery-gsap-plugin/">jQuery plugin</a>
* <a href="http://greensock.com/club/">Club GreenSock</a> (get access to bonus plugins and tools not in this repository)
* css-tricks.com article: <a href="https://css-tricks.com/myth-busting-css-animations-vs-javascript/">Myth Busting: CSS Animations vs. JavaScript</a>
* css-tricks.com article about how <a href="https://css-tricks.com/svg-animation-on-css-transforms/">GSAP solves cross-browser SVG animation challenges</a>

### Need help?
Head over to the <a href="http://greensock.com/forums/">GreenSock forums</a> which are an excellent resource for learning and getting your questions answered. Report any bugs there too please (it's also fine to file an issue on Github if you prefer)

Copyright (c) 2008-2015, GreenSock. All rights reserved. 

License: GreenSock's standard "no charge" license can be viewed at <a href="http://greensock.com/standard-license">http://greensock.com/standard-license</a>. <a href="http://greensock.com/club/">Club GreenSock</a> members are granted additional rights, as described in the license that comes with their membership. For more information about licensing, see <a href="http://greensock.com/licensing/">http://greensock.com/licensing/</a>.