/*!
 * VERSION: beta 1.3.4
 * DATE: 2014-11-15
 * UPDATES AND DOCS AT: http://www.greensock.com
 *
 * @license Copyright (c) 2008-2015, GreenSock. All rights reserved.
 * This work is subject to the terms at http://greensock.com/standard-license or for
 * Club GreenSock members, the software agreement that was issued with your membership.
 * 
 * @author: <PERSON>, <EMAIL>
 **/
var _gsScope="undefined"!=typeof module&&module.exports&&"undefined"!=typeof global?global:this||window;(_gsScope._gsQueue||(_gsScope._gsQueue=[])).push(function(){"use strict";var t=180/Math.PI,e=[],i=[],r=[],s={},n=_gsScope._gsDefine.globals,a=function(t,e,i,r){this.a=t,this.b=e,this.c=i,this.d=r,this.da=r-t,this.ca=i-t,this.ba=e-t},o=",x,y,z,left,top,right,bottom,marginTop,marginLeft,marginRight,marginBottom,paddingLeft,paddingTop,paddingRight,paddingBottom,backgroundPosition,backgroundPosition_y,",l=function(t,e,i,r){var s={a:t},n={},a={},o={c:r},l=(t+e)/2,h=(e+i)/2,u=(i+r)/2,_=(l+h)/2,f=(h+u)/2,c=(f-_)/8;return s.b=l+(t-l)/4,n.b=_+c,s.c=n.a=(s.b+n.b)/2,n.c=a.a=(_+f)/2,a.b=f-c,o.b=u+(r-u)/4,a.c=o.a=(a.b+o.b)/2,[s,n,a,o]},h=function(t,s,n,a,o){var h,u,_,f,c,p,d,m,g,v,y,T,x,w=t.length-1,b=0,P=t[0].a;for(h=0;w>h;h++)c=t[b],u=c.a,_=c.d,f=t[b+1].d,o?(y=e[h],T=i[h],x=.25*(T+y)*s/(a?.5:r[h]||.5),p=_-(_-u)*(a?.5*s:0!==y?x/y:0),d=_+(f-_)*(a?.5*s:0!==T?x/T:0),m=_-(p+((d-p)*(3*y/(y+T)+.5)/4||0))):(p=_-.5*(_-u)*s,d=_+.5*(f-_)*s,m=_-(p+d)/2),p+=m,d+=m,c.c=g=p,c.b=0!==h?P:P=c.a+.6*(c.c-c.a),c.da=_-u,c.ca=g-u,c.ba=P-u,n?(v=l(u,P,g,_),t.splice(b,1,v[0],v[1],v[2],v[3]),b+=4):b++,P=d;c=t[b],c.b=P,c.c=P+.4*(c.d-P),c.da=c.d-c.a,c.ca=c.c-c.a,c.ba=P-c.a,n&&(v=l(c.a,P,c.c,c.d),t.splice(b,1,v[0],v[1],v[2],v[3]))},u=function(t,r,s,n){var o,l,h,u,_,f,c=[];if(n)for(t=[n].concat(t),l=t.length;--l>-1;)"string"==typeof(f=t[l][r])&&"="===f.charAt(1)&&(t[l][r]=n[r]+Number(f.charAt(0)+f.substr(2)));if(o=t.length-2,0>o)return c[0]=new a(t[0][r],0,0,t[-1>o?0:1][r]),c;for(l=0;o>l;l++)h=t[l][r],u=t[l+1][r],c[l]=new a(h,0,0,u),s&&(_=t[l+2][r],e[l]=(e[l]||0)+(u-h)*(u-h),i[l]=(i[l]||0)+(_-u)*(_-u));return c[l]=new a(t[l][r],0,0,t[l+1][r]),c},_=function(t,n,a,l,_,f){var c,p,d,m,g,v,y,T,x={},w=[],b=f||t[0];_="string"==typeof _?","+_+",":o,null==n&&(n=1);for(p in t[0])w.push(p);if(t.length>1){for(T=t[t.length-1],y=!0,c=w.length;--c>-1;)if(p=w[c],Math.abs(b[p]-T[p])>.05){y=!1;break}y&&(t=t.concat(),f&&t.unshift(f),t.push(t[1]),f=t[t.length-3])}for(e.length=i.length=r.length=0,c=w.length;--c>-1;)p=w[c],s[p]=-1!==_.indexOf(","+p+","),x[p]=u(t,p,s[p],f);for(c=e.length;--c>-1;)e[c]=Math.sqrt(e[c]),i[c]=Math.sqrt(i[c]);if(!l){for(c=w.length;--c>-1;)if(s[p])for(d=x[w[c]],v=d.length-1,m=0;v>m;m++)g=d[m+1].da/i[m]+d[m].da/e[m],r[m]=(r[m]||0)+g*g;for(c=r.length;--c>-1;)r[c]=Math.sqrt(r[c])}for(c=w.length,m=a?4:1;--c>-1;)p=w[c],d=x[p],h(d,n,a,l,s[p]),y&&(d.splice(0,m),d.splice(d.length-m,m));return x},f=function(t,e,i){e=e||"soft";var r,s,n,o,l,h,u,_,f,c,p,d={},m="cubic"===e?3:2,g="soft"===e,v=[];if(g&&i&&(t=[i].concat(t)),null==t||m+1>t.length)throw"invalid Bezier data";for(f in t[0])v.push(f);for(h=v.length;--h>-1;){for(f=v[h],d[f]=l=[],c=0,_=t.length,u=0;_>u;u++)r=null==i?t[u][f]:"string"==typeof(p=t[u][f])&&"="===p.charAt(1)?i[f]+Number(p.charAt(0)+p.substr(2)):Number(p),g&&u>1&&_-1>u&&(l[c++]=(r+l[c-2])/2),l[c++]=r;for(_=c-m+1,c=0,u=0;_>u;u+=m)r=l[u],s=l[u+1],n=l[u+2],o=2===m?0:l[u+3],l[c++]=p=3===m?new a(r,s,n,o):new a(r,(2*s+r)/3,(2*s+n)/3,n);l.length=c}return d},c=function(t,e,i){for(var r,s,n,a,o,l,h,u,_,f,c,p=1/i,d=t.length;--d>-1;)for(f=t[d],n=f.a,a=f.d-n,o=f.c-n,l=f.b-n,r=s=0,u=1;i>=u;u++)h=p*u,_=1-h,r=s-(s=(h*h*a+3*_*(h*o+_*l))*h),c=d*i+u-1,e[c]=(e[c]||0)+r*r},p=function(t,e){e=e>>0||6;var i,r,s,n,a=[],o=[],l=0,h=0,u=e-1,_=[],f=[];for(i in t)c(t[i],a,e);for(s=a.length,r=0;s>r;r++)l+=Math.sqrt(a[r]),n=r%e,f[n]=l,n===u&&(h+=l,n=r/e>>0,_[n]=f,o[n]=h,l=0,f=[]);return{length:h,lengths:o,segments:_}},d=_gsScope._gsDefine.plugin({propName:"bezier",priority:-1,version:"1.3.4",API:2,global:!0,init:function(t,e,i){this._target=t,e instanceof Array&&(e={values:e}),this._func={},this._round={},this._props=[],this._timeRes=null==e.timeResolution?6:parseInt(e.timeResolution,10);var r,s,n,a,o,l=e.values||[],h={},u=l[0],c=e.autoRotate||i.vars.orientToBezier;this._autoRotate=c?c instanceof Array?c:[["x","y","rotation",c===!0?0:Number(c)||0]]:null;for(r in u)this._props.push(r);for(n=this._props.length;--n>-1;)r=this._props[n],this._overwriteProps.push(r),s=this._func[r]="function"==typeof t[r],h[r]=s?t[r.indexOf("set")||"function"!=typeof t["get"+r.substr(3)]?r:"get"+r.substr(3)]():parseFloat(t[r]),o||h[r]!==l[0][r]&&(o=h);if(this._beziers="cubic"!==e.type&&"quadratic"!==e.type&&"soft"!==e.type?_(l,isNaN(e.curviness)?1:e.curviness,!1,"thruBasic"===e.type,e.correlate,o):f(l,e.type,h),this._segCount=this._beziers[r].length,this._timeRes){var d=p(this._beziers,this._timeRes);this._length=d.length,this._lengths=d.lengths,this._segments=d.segments,this._l1=this._li=this._s1=this._si=0,this._l2=this._lengths[0],this._curSeg=this._segments[0],this._s2=this._curSeg[0],this._prec=1/this._curSeg.length}if(c=this._autoRotate)for(this._initialRotations=[],c[0]instanceof Array||(this._autoRotate=c=[c]),n=c.length;--n>-1;){for(a=0;3>a;a++)r=c[n][a],this._func[r]="function"==typeof t[r]?t[r.indexOf("set")||"function"!=typeof t["get"+r.substr(3)]?r:"get"+r.substr(3)]:!1;r=c[n][2],this._initialRotations[n]=this._func[r]?this._func[r].call(this._target):this._target[r]}return this._startRatio=i.vars.runBackwards?1:0,!0},set:function(e){var i,r,s,n,a,o,l,h,u,_,f=this._segCount,c=this._func,p=this._target,d=e!==this._startRatio;if(this._timeRes){if(u=this._lengths,_=this._curSeg,e*=this._length,s=this._li,e>this._l2&&f-1>s){for(h=f-1;h>s&&e>=(this._l2=u[++s]););this._l1=u[s-1],this._li=s,this._curSeg=_=this._segments[s],this._s2=_[this._s1=this._si=0]}else if(this._l1>e&&s>0){for(;s>0&&(this._l1=u[--s])>=e;);0===s&&this._l1>e?this._l1=0:s++,this._l2=u[s],this._li=s,this._curSeg=_=this._segments[s],this._s1=_[(this._si=_.length-1)-1]||0,this._s2=_[this._si]}if(i=s,e-=this._l1,s=this._si,e>this._s2&&_.length-1>s){for(h=_.length-1;h>s&&e>=(this._s2=_[++s]););this._s1=_[s-1],this._si=s}else if(this._s1>e&&s>0){for(;s>0&&(this._s1=_[--s])>=e;);0===s&&this._s1>e?this._s1=0:s++,this._s2=_[s],this._si=s}o=(s+(e-this._s1)/(this._s2-this._s1))*this._prec}else i=0>e?0:e>=1?f-1:f*e>>0,o=(e-i*(1/f))*f;for(r=1-o,s=this._props.length;--s>-1;)n=this._props[s],a=this._beziers[n][i],l=(o*o*a.da+3*r*(o*a.ca+r*a.ba))*o+a.a,this._round[n]&&(l=Math.round(l)),c[n]?p[n](l):p[n]=l;if(this._autoRotate){var m,g,v,y,T,x,w,b=this._autoRotate;for(s=b.length;--s>-1;)n=b[s][2],x=b[s][3]||0,w=b[s][4]===!0?1:t,a=this._beziers[b[s][0]],m=this._beziers[b[s][1]],a&&m&&(a=a[i],m=m[i],g=a.a+(a.b-a.a)*o,y=a.b+(a.c-a.b)*o,g+=(y-g)*o,y+=(a.c+(a.d-a.c)*o-y)*o,v=m.a+(m.b-m.a)*o,T=m.b+(m.c-m.b)*o,v+=(T-v)*o,T+=(m.c+(m.d-m.c)*o-T)*o,l=d?Math.atan2(T-v,y-g)*w+x:this._initialRotations[s],c[n]?p[n](l):p[n]=l)}}}),m=d.prototype;d.bezierThrough=_,d.cubicToQuadratic=l,d._autoCSS=!0,d.quadraticToCubic=function(t,e,i){return new a(t,(2*e+t)/3,(2*e+i)/3,i)},d._cssRegister=function(){var t=n.CSSPlugin;if(t){var e=t._internals,i=e._parseToProxy,r=e._setPluginRatio,s=e.CSSPropTween;e._registerComplexSpecialProp("bezier",{parser:function(t,e,n,a,o,l){e instanceof Array&&(e={values:e}),l=new d;var h,u,_,f=e.values,c=f.length-1,p=[],m={};if(0>c)return o;for(h=0;c>=h;h++)_=i(t,f[h],a,o,l,c!==h),p[h]=_.end;for(u in e)m[u]=e[u];return m.values=p,o=new s(t,"bezier",0,0,_.pt,2),o.data=_,o.plugin=l,o.setRatio=r,0===m.autoRotate&&(m.autoRotate=!0),!m.autoRotate||m.autoRotate instanceof Array||(h=m.autoRotate===!0?0:Number(m.autoRotate),m.autoRotate=null!=_.end.left?[["left","top","rotation",h,!1]]:null!=_.end.x?[["x","y","rotation",h,!1]]:!1),m.autoRotate&&(a._transform||a._enableTransforms(!1),_.autoRotate=a._target._gsTransform),l._onInitTween(_.proxy,m,a._tween),o}})}},m._roundProps=function(t,e){for(var i=this._overwriteProps,r=i.length;--r>-1;)(t[i[r]]||t.bezier||t.bezierThrough)&&(this._round[i[r]]=e)},m._kill=function(t){var e,i,r=this._props;for(e in this._beziers)if(e in t)for(delete this._beziers[e],delete this._func[e],i=r.length;--i>-1;)r[i]===e&&r.splice(i,1);return this._super._kill.call(this,t)}}),_gsScope._gsDefine&&_gsScope._gsQueue.pop()(),function(t){"use strict";var e=function(){return(_gsScope.GreenSockGlobals||_gsScope)[t]};"function"==typeof define&&define.amd?define(["TweenLite"],e):"undefined"!=typeof module&&module.exports&&(require("../TweenLite.js"),module.exports=e())}("BezierPlugin");